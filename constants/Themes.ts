import { DefaultTheme } from 'styled-components';
import Colors, { brand } from './Colors';

export const lightTheme = {
  text: Colors.light.text,
  background: Colors.light.background,
  tint: Colors.light.tint,
  tabIconDefault: Colors.light.tabIconDefault,
  tabIconSelected: Colors.light.tabIconSelected,
  brand: {
    green: {
      100: brand.green[100],
      200: brand.green[200],
      300: brand.green[300],
      400: brand.green[400],
      500: brand.green[500],
      600: brand.green[600],
      700: brand.green[700],
      800: brand.green[800],
      900: brand.green[900],
    },
    orange: brand.orange,
    magenta: brand.magenta,
    skyBlue: brand.skyBlue,
    red: brand.red,
  },
  // TODO: Update these values
  borderRadius: '4px',
  colors: {
    primary: '#6200ee',
    secondary: '#03dac6',
  },
};

export const darkTheme: DefaultTheme = {
  text: Colors.dark.text,
  background: Colors.dark.background,
  tint: Colors.dark.tint,
  tabIconDefault: Colors.dark.tabIconDefault,
  tabIconSelected: Colors.dark.tabIconSelected,
  brand: {
    green: {
      100: brand.green[100],
      200: brand.green[200],
      300: brand.green[300],
      400: brand.green[400],
      500: brand.green[500],
      600: brand.green[600],
      700: brand.green[700],
      800: brand.green[800],
      900: brand.green[900],
    },
    orange: brand.orange,
    magenta: brand.magenta,
    skyBlue: brand.skyBlue,
    red: brand.red,
  },
  // TODO: Update these values
  borderRadius: '4px',
  colors: {
    primary: '#6200ee',
    secondary: '#03dac6',
  },
};

export interface Theme {
  brand: {
    green: string;
    orange: string;
    magenta: string;
    skyBlue: string;
  };
  borderRadius: string;
  colors: {
    primary: string;
    secondary: string;
  };
}

export const Themes = {
  light: lightTheme,
  dark: darkTheme,
};
