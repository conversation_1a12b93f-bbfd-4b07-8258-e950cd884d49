import styled from 'styled-components/native';

export const OverviewItemContainer = styled.TouchableOpacity<{ isItem: boolean }>`
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  height: ${({ isItem }: { isItem: boolean }) => isItem ? '40px' : '30px'};
  margin-bottom: 10px;
  ${({ isItem }: { isItem: boolean }) => !isItem && 'margin-bottom: 0px;'}
`;

export const OverviewItemText = styled.Text`
  font-family: 'MuktaVaani';
  font-size: 16px;
`;

export const OverviewItemTextBold = styled.Text`
  font-family: 'MuktaVaani-Bold';
  font-size: 16px;
`;

export const ItemCountText = styled.Text`
  font-family: 'MuktaVaani';
  font-size: 16px;
`;

export const QuantityText = styled.Text`
  font-family: 'MuktaVaani-Regular';
  font-size: 16px;
`;

export const CircleIconContainer = styled.TouchableOpacity`
  width: 20px;
  height: 20px;
  border-radius: 10px;
  background-color: #0E7E61;
  align-items: center;
  justify-content: center;
`;

export const BrandColorText = styled.Text`
  font-family: 'MuktaVaani';
  font-size: 13px;
  font-weight: 400;
  color: gray;
`;
