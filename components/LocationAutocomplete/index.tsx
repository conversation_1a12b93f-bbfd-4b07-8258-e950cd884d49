import { GOOGLE_PLACES_API_KEY } from '@/constants/api-keys';
import { useQuery } from '@tanstack/react-query';
import React, { forwardRef, useCallback, useEffect, useImperativeHandle, useRef, useState } from 'react';
import { Text, TouchableOpacity, View } from 'react-native';
import Autocomplete from 'react-native-autocomplete-input';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';

interface LocationData {
  id: string;
  name: string;
  countryCode: string;
  coords: {
    lat: number;
    lng: number;
  };
}

interface LocationAutocompleteProps {
  value: string;
  onLocationSelect: (location: {
    name: string;
    latitude: number;
    longitude: number;
    countryCode?: string;
  }) => void;
  placeholder?: string;
  scrollRef?: React.RefObject<KeyboardAwareScrollView>;
  style?: object;
}

const fetchPlacePredictions = async (text: string): Promise<LocationData[]> => {
  if (text.length <= 2) return [];

  const response = await fetch(
    `https://maps.googleapis.com/maps/api/place/autocomplete/json?input=${encodeURIComponent(
      text,
    )}&types=(cities)&key=${GOOGLE_PLACES_API_KEY}`,
  );

  const data = await response.json();
  if (!data.predictions) return [];
  console.log(data.predictions, 'Tumiloy');

  return Promise.all(
    data.predictions.map(
      async (prediction: { place_id: any; description: any }) => {
        const detailsResponse = await fetch(
          `https://maps.googleapis.com/maps/api/place/details/json?place_id=${prediction.place_id}&fields=geometry,address_components&key=${GOOGLE_PLACES_API_KEY}`,
        );

        const detailsData = await detailsResponse.json();

        // Extract country code from address_components
        const countryComponent = detailsData.result.address_components.find(
          (component: any) => component.types.includes('country'),
        );
        const countryCode = countryComponent ? countryComponent.short_name : '';

        return {
          id: prediction.place_id,
          name: prediction.description,
          countryCode,
          coords: {
            lat: detailsData.result.geometry.location.lat,
            lng: detailsData.result.geometry.location.lng,
          },
        };
      },
    ),
  );
};

const LocationAutocomplete = forwardRef<any, LocationAutocompleteProps>(({
  value = '',
  onLocationSelect,
  placeholder = 'Search City',
  scrollRef,
  style,
}, ref) => {
  const [query, setQuery] = useState(value);
  const [showAutocomplete, setShowAutocomplete] = useState(false);
  const autocompleteRef = useRef<any>(null);

  useImperativeHandle(ref, () => ({
    focus: () => {
      autocompleteRef.current?.focus();
    },
  }));

  const { data: places = [], isLoading } = useQuery({
    queryKey: ['places', query],
    queryFn: () => fetchPlacePredictions(query),
    enabled: query.length > 2 && showAutocomplete,
    staleTime: 1000 * 60 * 5, // 5 minutes
  });

  useEffect(() => {
    setQuery(value);
  }, [value]);

  const handleQueryChange = useCallback((text: string) => {
    setQuery(text);
    setShowAutocomplete(true);
    // Reset selection when user types
    if (text === '') {
      onLocationSelect({
        name: '',
        latitude: 0,
        longitude: 0,
        countryCode: '',
      });
    }
  }, [value, onLocationSelect]);

  const handleLocationSelect = useCallback(
    (item: LocationData) => {
      onLocationSelect({
        name: item.name,
        latitude: item.coords.lat,
        longitude: item.coords.lng,
        countryCode: item.countryCode,
      });
      setQuery(item.name);
      setShowAutocomplete(false);
    },
    [onLocationSelect],
  );

  return (
    <View style={{ width: '100%', zIndex: 1000 }}>
      <Autocomplete
        ref={autocompleteRef}
        data={showAutocomplete ? places.slice(0, 3) : []}
        value={query}
        onChangeText={handleQueryChange}
        onFocus={(e) => {
          setShowAutocomplete(true);
          scrollRef?.current?.scrollToFocusedInput(e.target);
        }}
        placeholder={isLoading ? 'Loading...' : placeholder}
        flatListProps={{
          keyExtractor: (item: LocationData) => item.id,
          renderItem: ({ item }: { item: LocationData }) => (
            <TouchableOpacity
              onPress={() => handleLocationSelect(item)}
              style={{
                padding: 10,
                borderBottomWidth: 1,
                borderBottomColor: '#ddd',
              }}
            >
              <Text style={{ fontFamily: 'MuktaVaani', fontSize: 12 }}>
                {item.name}
              </Text>
            </TouchableOpacity>
          ),
          removeClippedSubviews: false,
          scrollEnabled: false,
          bounces: false,
          showsVerticalScrollIndicator: false,
          nestedScrollEnabled: true,
        }}
        placeholderTextColor="#999999"
        style={{
          backgroundColor: 'transparent',
          width: '100%',
          height: 30,
          textAlign: 'right',
          margin: 0,
          padding: 0,
          fontFamily: 'MuktaVaani',
          fontSize: 16,
          zIndex: 0,
          ...style,
        }}
        textAlign="right"
        inputContainerStyle={{
          borderWidth: 0,
          padding: 0,
          margin: 0,
        }}
        containerStyle={{
          position: 'relative',
          padding: 0,
          margin: 0,
          zIndex: 1,
        }}
        listContainerStyle={{
          position: 'absolute',
          top: '100%',
          right: 0,
          left: 0,
          zIndex: 9999,
          borderRadius: 4,
          padding: 0,
          margin: 0,
          shadowColor: '#000',
          shadowOffset: {
            width: 0,
            height: 2,
          },
          shadowOpacity: 0.25,
          shadowRadius: 3.84,
          elevation: 5,
        }}
      />
    </View>
  );
});

export default LocationAutocomplete;
