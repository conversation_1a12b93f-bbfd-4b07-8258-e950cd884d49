import { ActivityIndicator, FlatList, LayoutChangeEvent, TouchableOpacity, View } from 'react-native';
import {
  AddClothsModalContainer,
  AddClothsModalHeader,
  AddClothsModalTitle,
  HeaderText,
  AddClothsModalInput,
  ClothItemContainer,
  ClothItemName,
  ClothesContainer,
  AddClothItemContainer,
  CategoryItem,
  CategoryItemText,
  ClothItemImage,
  ClothNameContainer
} from './styles';
import Modal from 'react-native-modal';
import { XIcon, PlusIcon } from 'lucide-react-native';
import Input from '../common/Input';
import { Checkbox } from '@/components/common/Checkbox';
import { useState, useEffect, useMemo } from 'react';
import {
  createCategoryIdToNameMap,
  getItemCategoryName,
  filterItemsByCategory,
  filterCategoriesWithItems
} from '../../utils/categoryMapping';
import { CLOTHES, CLOTHES_CATEGORIES } from '@/constants/Clothes';
import { getCategories, getClothes, UploadImage } from '@/methods/cloths';
// Import API-based categories
import { fetchCategoriesFromBackend } from '@/data/gender-categories';
import { getCurrentUserGender } from '@/data/categories';
import { getUserProfile } from '@/methods/users';
import { getStandardizedCategories, formatCategoriesForDisplay } from '@/utils/standardCategories';



interface AddClothsCategoryModalProps {
  isVisible: boolean;
  onClose: () => void;
  onAdd: (item: any) => void;
  item: any;
  packingList: any[];
  addedCloths: any[];
  setIsAddItemsModalVisible: (visible: boolean) => void;
}

export default function AddClothsCategoryModal({ isVisible, onClose, onAdd, item, packingList = [], addedCloths = [], setIsAddItemsModalVisible }: AddClothsCategoryModalProps) {

  const { mutate: uploadImage } = UploadImage();

  const [search, setSearch] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [clothes, setClothes] = useState<any[]>([]);
  const [width, setWidth] = useState(0);
  const [image, setImage] = useState<string | null>(null);
  const [selectedClothes, setSelectedClothes] = useState<any[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<any>({ _id: 'all', name: 'All', category: 'All' });
  // State for gender-specific categories
  const [displayCategories, setDisplayCategories] = useState<any[]>([{ _id: 'all', name: 'All', category: 'All' }]);

  const { data: categories, isLoading: isCategoriesLoading, refetch: refetchCategories } = getCategories();
  const { data: clothesList, isLoading: isClothesLoading, refetch: refetchClothes } = getClothes();

  // Create category ID to name mapping using backend categories
  const categoryIdToNameMap = useMemo(() => {
    // Backend returns: { success: true, data: { itemCategories: [...] } }
    return createCategoryIdToNameMap(categories?.data?.itemCategories || []);
  }, [categories]);

  // Memoized categories that only show categories with actual items, sorted alphabetically
  const categoriesWithItems = useMemo(() => {
    if (!clothesList?.items || !displayCategories) {
      return [{ _id: 'all', name: 'All', category: 'All' }];
    }

    // Get available items (excluding already added items)
    const availableItems = clothesList.items.filter((item: any) => !addedCloths.includes(item._id));

    // Use shared utility to filter categories with items
    return filterCategoriesWithItems(displayCategories, availableItems, categoryIdToNameMap);
  }, [displayCategories, clothesList?.items, addedCloths, categoryIdToNameMap]);
  // Get user profile to determine gender
  const { data: userProfile } = getUserProfile();

  const handleLayout = (event: LayoutChangeEvent) => {
    const { width } = event.nativeEvent.layout;
    setWidth(width);
  };

  const addSelectedCloth = (cloth: any) => {
    cloth.quantity = 1;
    setSelectedClothes([...selectedClothes, cloth]);
  }

  const removeSelectedCloth = (cloth: any) => {
    setSelectedClothes(selectedClothes.filter((item) => item._id !== cloth._id));
  }

  useEffect(() => {
    refetchClothes();
  }, [isVisible]);

  // Load gender-specific categories
  useEffect(() => {
    const loadGenderCategories = async () => {
      try {
        // Get gender from user profile
        let gender = null;
        if (userProfile?.data?.profile?.gender) {
          gender = userProfile.data.profile.gender;
          console.log('AddClothsCategoryModal - Using gender from userProfile:', gender);
        } else {
          // Try to get gender from Meteor.user()
          gender = getCurrentUserGender();
          console.log('AddClothsCategoryModal - Using gender from getCurrentUserGender:', gender);
        }

        if (!gender) {
          console.warn('AddClothsCategoryModal - No gender available, using default categories');
          // Fallback to backend categories if there's an error
          if (categories?.data?.itemCategories && categories.data.itemCategories.length > 0) {
            setDisplayCategories([
              { _id: 'all', name: 'All', category: 'All' },
              ...categories.data.itemCategories.map((cat: any) => ({
                _id: cat._id,
                name: cat.mainCategory || cat.name || 'Unknown',
                category: cat.mainCategory || cat.name || 'Unknown'
              }))
            ]);
          }
          return;
        }

        console.log('\n🔍 ADD CLOTHS CATEGORY MODAL - LOADING STANDARDIZED CATEGORIES 🔍');

        try {
          // Map frontend gender values to backend expected values
          const backendGender = gender === 'Male' ? 'men' : gender === 'Female' ? 'women' : gender;
          console.log('AddClothsCategoryModal - Mapped to backend gender:', backendGender);

          // Get standardized categories that include both backend and standard categories
          const standardizedCategories = await getStandardizedCategories(backendGender);

          console.log(`Loaded ${standardizedCategories.length} standardized categories`);

          // Format categories for display in the AddClothsCategoryModal
          // We need to use _id instead of id and include the category property
          const formattedCategories = formatCategoriesForDisplay(
            standardizedCategories,
            true,  // Include "All" category
            '_id', // Use _id property instead of id
            true   // Include category property
          );

          // Log the categories that will be displayed
          console.log('=== CATEGORIES DISPLAYED IN ADD CLOTHS CATEGORY MODAL ===');
          formattedCategories.forEach((cat, index) => {
            console.log(`${index + 1}. ${cat.name} (ID: ${cat._id})`);
          });
          console.log('=== END DISPLAYED CATEGORIES ===');

          setDisplayCategories(formattedCategories);
        } catch (error) {
          console.error('Error getting standardized categories:', error);

          // Fallback to backend categories if there's an error
          if (categories?.data?.itemCategories && categories.data.itemCategories.length > 0) {
            setDisplayCategories([
              { _id: 'all', name: 'All', category: 'All' },
              ...categories.data.itemCategories.map((cat: any) => ({
                _id: cat._id,
                name: cat.mainCategory || cat.name || 'Unknown',
                category: cat.mainCategory || cat.name || 'Unknown'
              }))
            ]);
          }
        }
      } catch (error) {
        console.error('Error loading gender-specific categories:', error);
        // Fallback to backend categories if there's an error
        if (categories?.data?.itemCategories && categories.data.itemCategories.length > 0) {
          setDisplayCategories([
            { _id: 'all', name: 'All', category: 'All' },
            ...categories.data.itemCategories.map((cat: any) => ({
              _id: cat._id,
              name: cat.mainCategory || cat.name || 'Unknown',
              category: cat.mainCategory || cat.name || 'Unknown'
            }))
          ]);
        }
      }
    };

    loadGenderCategories();
  }, [userProfile, categories]);


  const filteredPackingListClothes = clothesList?.items?.filter((item: any) => {
    // Skip items that are already added
    if (addedCloths.includes(item._id)) {
      return false;
    }

    // Apply search filter
    const matchesSearch = item?.name?.toLowerCase().includes(search.toLowerCase());
    if (!matchesSearch) {
      return false;
    }

    // Use shared utility to filter items by category
    return filterItemsByCategory([item], selectedCategory.name, categoryIdToNameMap).length > 0;
  });


  return <Modal
    key={item?.id}
    style={{ justifyContent: 'flex-end', margin: 0 }}
    isVisible={isVisible} onBackButtonPress={onClose} onBackdropPress={onClose}>

    <AddClothsModalContainer>
      <AddClothsModalHeader>
        <TouchableOpacity onPress={onClose}>
          <HeaderText>Close</HeaderText>
        </TouchableOpacity>
        <AddClothsModalTitle>
          Clothes
        </AddClothsModalTitle>
        <TouchableOpacity onPress={() => {
          onAdd(selectedClothes);
          setSelectedClothes([]);
          onClose();
        }}>
          <HeaderText>Add</HeaderText>
        </TouchableOpacity>
      </AddClothsModalHeader>
      <View style={{ marginTop: 20 }}>
        <AddClothsModalInput placeholder="Search"
          value={search}
          onChangeText={setSearch}
        />
      </View>
      <View style={{ marginTop: 20 }}>
        <FlatList
          data={categoriesWithItems}
          horizontal
          keyExtractor={(item) => item._id || item.name}
          showsHorizontalScrollIndicator={false}
          renderItem={({ item }) => {
            return <CategoryItem
              key={item._id || `category-${item.name}`}
              isSelected={selectedCategory.name === item.name}
              onPress={() => setSelectedCategory(item)}>
              <CategoryItemText isSelected={selectedCategory.name === item.name}>{item.name}</CategoryItemText>
            </CategoryItem>
          }}
        />
      </View>
      <ClothesContainer
        onLayout={handleLayout}
        style={{ marginTop: 20 }}>
        <FlatList
          keyExtractor={(item) => item._id}
          contentContainerStyle={{ paddingBottom: 200 }}
          data={[{ addItem: true }, ...filteredPackingListClothes || []]}
          numColumns={3}
          renderItem={({ item }) => {
            if (item.addItem) {
              return (
                <AddClothItemContainer
                  key="add-item-container"
                  width={width}
                  onPress={() => {
                    onClose();
                    setTimeout(() => {
                      setIsAddItemsModalVisible(true);
                      setSelectedClothes([]);
                    }, 500);
                  }}>
                  <PlusIcon size={25} color="#767676" />
                </AddClothItemContainer>
              )
            }
            return (
              <TouchableOpacity
                key={item._id || `item-${item.name}-${Math.random()}`}
                onPress={() => selectedClothes.includes(item) ? removeSelectedCloth(item) : addSelectedCloth(item)}>
                <ClothItemContainer width={width} isSelected={selectedClothes.includes(item) || false}>
                  <ClothItemImage source={item.imageUrl ? { uri: item.imageUrl } : require('@/assets/images/placeholder-item.png')} />

                  <Checkbox
                    checked={!!selectedClothes.includes(item)}
                    onToggle={() => selectedClothes.includes(item) ? removeSelectedCloth(item) : addSelectedCloth(item)}
                    size={20}
                    containerStyle={{ position: 'absolute', bottom: 8, right: 8 }}
                  />
                </ClothItemContainer>
                <ClothNameContainer>
                    <ClothItemName>{item.name}</ClothItemName>
                  </ClothNameContainer>
              </TouchableOpacity>
            )
          }}
        />
      </ClothesContainer>

    </AddClothsModalContainer>

  </Modal>;
}