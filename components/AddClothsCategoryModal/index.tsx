import { ActivityIndicator, FlatList, LayoutChangeEvent, TouchableOpacity, View } from 'react-native';
import {
  AddClothsModalContainer,
  AddClothsModalHeader,
  AddClothsModalTitle,
  HeaderText,
  AddClothsModalInput,
  ClothItemContainer,
  ClothItemName,
  ClothesContainer,
  AddClothItemContainer,
  CategoryItem,
  CategoryItemText,
  ClothItemImage,
  ClothNameContainer
} from './styles';
import Modal from 'react-native-modal';
import { XIcon, PlusIcon } from 'lucide-react-native';
import Input from '../common/Input';
import { Checkbox } from '@/components/common/Checkbox';
import { useState, useEffect, useMemo } from 'react';
import { CLOTHES, CLOTHES_CATEGORIES } from '@/constants/Clothes';
import { getCategories, getClothes, UploadImage } from '@/methods/cloths';
// Import API-based categories
import { fetchCategoriesFromBackend } from '@/data/gender-categories';
import { getCurrentUserGender } from '@/data/categories';
import { getUserProfile } from '@/methods/users';
import { getStandardizedCategories, formatCategoriesForDisplay } from '@/utils/standardCategories';



interface AddClothsCategoryModalProps {
  isVisible: boolean;
  onClose: () => void;
  onAdd: (item: any) => void;
  item: any;
  packingList: any[];
  addedCloths: any[];
  setIsAddItemsModalVisible: (visible: boolean) => void;
}

export default function AddClothsCategoryModal({ isVisible, onClose, onAdd, item, packingList = [], addedCloths = [], setIsAddItemsModalVisible }: AddClothsCategoryModalProps) {

  const { mutate: uploadImage } = UploadImage();

  const [search, setSearch] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [clothes, setClothes] = useState<any[]>([]);
  const [width, setWidth] = useState(0);
  const [image, setImage] = useState<string | null>(null);
  const [selectedClothes, setSelectedClothes] = useState<any[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<any>({ _id: 'all', name: 'All', category: 'All' });
  // State for gender-specific categories
  const [displayCategories, setDisplayCategories] = useState<any[]>([{ _id: 'all', name: 'All', category: 'All' }]);

  const { data: categories, isLoading: isCategoriesLoading, refetch: refetchCategories } = getCategories();
  const { data: clothesList, isLoading: isClothesLoading, refetch: refetchClothes } = getClothes();

  // Helper function to create a mapping from category IDs to category names
  const createCategoryIdToNameMap = (): Map<string, string> => {
    const idToNameMap = new Map<string, string>();

    if (!displayCategories) return idToNameMap;

    displayCategories.forEach((category: any) => {
      if (category._id && category.name) {
        idToNameMap.set(category._id.toLowerCase(), category.name.toLowerCase());
      }
    });

    return idToNameMap;
  };

  // Helper function to extract unique categories from items (updated to handle category IDs properly)
  const getUsedCategories = (items: any[]): Set<string> => {
    const usedCategories = new Set<string>();

    if (!items || !Array.isArray(items)) {
      return usedCategories;
    }

    // Create mapping from category IDs to names
    const categoryIdToNameMap = createCategoryIdToNameMap();

    items.forEach((item, index) => {
      let categoryFound = false;



      // First, try to get category from item.category.mainCategory (new schema)
      if (item?.category?.mainCategory) {
        const categoryName = item.category.mainCategory.toLowerCase().trim();
        usedCategories.add(categoryName);
        categoryFound = true;
      }

      // Fallback to item.category.name (old schema)
      if (!categoryFound && item?.category?.name) {
        const categoryName = item.category.name.toLowerCase().trim();
        usedCategories.add(categoryName);
        categoryFound = true;
      }

      // If category is a string instead of object
      if (!categoryFound && typeof item?.category === 'string') {
        const categoryName = item.category.toLowerCase().trim();
        usedCategories.add(categoryName);
        categoryFound = true;
      }

      // Try categoryName field
      if (!categoryFound && item?.categoryName) {
        const categoryName = item.categoryName.toLowerCase().trim();
        usedCategories.add(categoryName);
        categoryFound = true;
      }

      // Handle itemCategoryId - try to map ID to category name first
      if (!categoryFound && item?.itemCategoryId) {
        const itemCategoryId = item.itemCategoryId.toLowerCase().trim();

        // First, try to map the ID to a category name
        if (categoryIdToNameMap.has(itemCategoryId)) {
          const categoryName = categoryIdToNameMap.get(itemCategoryId)!;
          usedCategories.add(categoryName);
          categoryFound = true;
        } else {
          // If it's already a category name (like "dresses", "shoes"), use it directly
          let categoryName = '';

          // Handle basic- prefixed categories
          if (item.itemCategoryId.startsWith('basic-')) {
            categoryName = item.itemCategoryId.replace('basic-', '');
          } else if (item.itemCategoryId.startsWith('standard-')) {
            categoryName = item.itemCategoryId.replace('standard-', '');
          } else {
            categoryName = item.itemCategoryId;
          }

          categoryName = categoryName.toLowerCase().trim();
          usedCategories.add(categoryName);
          categoryFound = true;
        }
      }
    });

    return usedCategories;
  };

  // Memoized categories that only show categories with actual items, sorted alphabetically
  const categoriesWithItems = useMemo(() => {
    if (!clothesList?.items || !displayCategories) {
      return [{ _id: 'all', name: 'All', category: 'All' }];
    }

    // Get available items (excluding already added items)
    const availableItems = clothesList.items.filter((item: any) => !addedCloths.includes(item._id));

    // Get categories that have items using the same logic as Side Filter Panel
    const usedCategories = getUsedCategories(availableItems);

    // Filter displayCategories to show only those that have items
    const filteredCategories = displayCategories.filter((category: any) => {
      // Always include "All" category
      if (category.name === 'All') {
        return true;
      }

      // Check if this category has items
      const categoryNameLower = category.name.toLowerCase().trim();
      return usedCategories.has(categoryNameLower);
    });

    // Sort categories alphabetically (keeping "All" first)
    const sortedCategories = filteredCategories.sort((a: any, b: any) => {
      // Keep "All" category first
      if (a.name === 'All') return -1;
      if (b.name === 'All') return 1;

      // Sort others alphabetically
      return a.name.localeCompare(b.name);
    });

    return sortedCategories;
  }, [displayCategories, clothesList?.items, addedCloths]);
  // Get user profile to determine gender
  const { data: userProfile } = getUserProfile();

  const handleLayout = (event: LayoutChangeEvent) => {
    const { width } = event.nativeEvent.layout;
    setWidth(width);
  };

  const addSelectedCloth = (cloth: any) => {
    cloth.quantity = 1;
    setSelectedClothes([...selectedClothes, cloth]);
  }

  const removeSelectedCloth = (cloth: any) => {
    setSelectedClothes(selectedClothes.filter((item) => item._id !== cloth._id));
  }

  useEffect(() => {
    refetchClothes();
  }, [isVisible]);

  // Load gender-specific categories
  useEffect(() => {
    const loadGenderCategories = async () => {
      try {
        // Get gender from user profile
        let gender = null;
        if (userProfile?.data?.profile?.gender) {
          gender = userProfile.data.profile.gender;
          console.log('AddClothsCategoryModal - Using gender from userProfile:', gender);
        } else {
          // Try to get gender from Meteor.user()
          gender = getCurrentUserGender();
          console.log('AddClothsCategoryModal - Using gender from getCurrentUserGender:', gender);
        }

        if (!gender) {
          console.warn('AddClothsCategoryModal - No gender available, using default categories');
          // Fallback to backend categories if there's an error
          if (categories?.itemCategories && categories.itemCategories.length > 0) {
            setDisplayCategories([
              { _id: 'all', name: 'All', category: 'All' },
              ...categories.itemCategories
            ]);
          }
          return;
        }

        console.log('\n🔍 ADD CLOTHS CATEGORY MODAL - LOADING STANDARDIZED CATEGORIES 🔍');

        try {
          // Get standardized categories that include both backend and standard categories
          const standardizedCategories = await getStandardizedCategories(gender);

          console.log(`Loaded ${standardizedCategories.length} standardized categories`);

          // Format categories for display in the AddClothsCategoryModal
          // We need to use _id instead of id and include the category property
          const formattedCategories = formatCategoriesForDisplay(
            standardizedCategories,
            true,  // Include "All" category
            '_id', // Use _id property instead of id
            true   // Include category property
          );

          // Log the categories that will be displayed
          console.log('=== CATEGORIES DISPLAYED IN ADD CLOTHS CATEGORY MODAL ===');
          formattedCategories.forEach((cat, index) => {
            console.log(`${index + 1}. ${cat.name} (ID: ${cat._id})`);
          });
          console.log('=== END DISPLAYED CATEGORIES ===');

          setDisplayCategories(formattedCategories);
        } catch (error) {
          console.error('Error getting standardized categories:', error);

          // Fallback to backend categories if there's an error
          if (categories?.itemCategories && categories.itemCategories.length > 0) {
            setDisplayCategories([
              { _id: 'all', name: 'All', category: 'All' },
              ...categories.itemCategories
            ]);
          }
        }
      } catch (error) {
        console.error('Error loading gender-specific categories:', error);
        // Fallback to backend categories if there's an error
        if (categories?.itemCategories && categories.itemCategories.length > 0) {
          setDisplayCategories([
            { _id: 'all', name: 'All', category: 'All' },
            ...categories.itemCategories
          ]);
        }
      }
    };

    loadGenderCategories();
  }, [userProfile, categories]);


  // Helper function to get category name from item
  const getItemCategoryName = (item: any): string => {
    // Try different category fields in order of preference
    if (item?.category?.mainCategory) {
      return item.category.mainCategory;
    }
    if (item?.category?.name) {
      return item.category.name;
    }
    if (typeof item?.category === 'string') {
      return item.category;
    }
    if (item?.categoryName) {
      return item.categoryName;
    }

    // Handle itemCategoryId - try to map to category name or use as-is
    if (item?.itemCategoryId) {
      const categoryIdToNameMap = createCategoryIdToNameMap();
      const itemCategoryId = item.itemCategoryId.toLowerCase().trim();

      // Try to map ID to name first
      if (categoryIdToNameMap.has(itemCategoryId)) {
        return categoryIdToNameMap.get(itemCategoryId)!;
      }

      // If it's already a category name (like "dresses", "shoes"), use it directly
      if (item.itemCategoryId.startsWith('basic-')) {
        return item.itemCategoryId.replace('basic-', '');
      } else if (item.itemCategoryId.startsWith('standard-')) {
        return item.itemCategoryId.replace('standard-', '');
      } else {
        return item.itemCategoryId;
      }
    }

    return '';
  };

  const filteredPackingListClothes = clothesList?.items?.filter((item) => {
    // Skip items that are already added
    if (addedCloths.includes(item._id)) {
      return false;
    }

    // Apply search filter
    const matchesSearch = item?.name?.toLowerCase().includes(search.toLowerCase());
    if (!matchesSearch) {
      return false;
    }

    // If "All" category is selected, show all items that match the search
    if (selectedCategory.name === 'All') {
      return true;
    }

    // Get the item's category name using the helper function
    const itemCategory = getItemCategoryName(item);
    const filterCategory = selectedCategory.name;

    if (!itemCategory) {
      return false;
    }

    // Exact match (case-insensitive)
    return itemCategory.toLowerCase().trim() === filterCategory.toLowerCase().trim();
  });


  return <Modal
    key={item?.id}
    style={{ justifyContent: 'flex-end', margin: 0 }}
    isVisible={isVisible} onBackButtonPress={onClose} onBackdropPress={onClose}>

    <AddClothsModalContainer>
      <AddClothsModalHeader>
        <TouchableOpacity onPress={onClose}>
          <HeaderText>Close</HeaderText>
        </TouchableOpacity>
        <AddClothsModalTitle>
          Clothes
        </AddClothsModalTitle>
        <TouchableOpacity onPress={() => {
          onAdd(selectedClothes);
          setSelectedClothes([]);
          onClose();
        }}>
          <HeaderText>Add</HeaderText>
        </TouchableOpacity>
      </AddClothsModalHeader>
      <View style={{ marginTop: 20 }}>
        <AddClothsModalInput placeholder="Search"
          value={search}
          onChangeText={setSearch}
        />
      </View>
      <View style={{ marginTop: 20 }}>
        <FlatList
          data={categoriesWithItems}
          horizontal
          keyExtractor={(item) => item._id || item.name}
          showsHorizontalScrollIndicator={false}
          renderItem={({ item }) => {
            return <CategoryItem
              key={item._id || `category-${item.name}`}
              isSelected={selectedCategory.name === item.name}
              onPress={() => setSelectedCategory(item)}>
              <CategoryItemText isSelected={selectedCategory.name === item.name}>{item.name}</CategoryItemText>
            </CategoryItem>
          }}
        />
      </View>
      <ClothesContainer
        onLayout={handleLayout}
        style={{ marginTop: 20 }}>
        <FlatList
          keyExtractor={(item) => item._id}
          contentContainerStyle={{ paddingBottom: 200 }}
          data={[{ addItem: true }, ...filteredPackingListClothes || []]}
          numColumns={3}
          renderItem={({ item }) => {
            if (item.addItem) {
              return (
                <AddClothItemContainer
                  key="add-item-container"
                  width={width}
                  onPress={() => {
                    onClose();
                    setTimeout(() => {
                      setIsAddItemsModalVisible(true);
                      setSelectedClothes([]);
                    }, 500);
                  }}>
                  <PlusIcon size={25} color="#767676" />
                </AddClothItemContainer>
              )
            }
            return (
              <TouchableOpacity
                key={item._id || `item-${item.name}-${Math.random()}`}
                onPress={() => selectedClothes.includes(item) ? removeSelectedCloth(item) : addSelectedCloth(item)}>
                <ClothItemContainer width={width} isSelected={selectedClothes.includes(item) || false}>
                  <ClothItemImage source={item.imageUrl ? { uri: item.imageUrl } : require('@/assets/images/placeholder-item.png')} />

                  <Checkbox
                    checked={!!selectedClothes.includes(item)}
                    onToggle={() => selectedClothes.includes(item) ? removeSelectedCloth(item) : addSelectedCloth(item)}
                    size={20}
                    containerStyle={{ position: 'absolute', bottom: 8, right: 8 }}
                  />
                </ClothItemContainer>
                <ClothNameContainer>
                    <ClothItemName>{item.name}</ClothItemName>
                  </ClothNameContainer>
              </TouchableOpacity>
            )
          }}
        />
      </ClothesContainer>

    </AddClothsModalContainer>

  </Modal>;
}