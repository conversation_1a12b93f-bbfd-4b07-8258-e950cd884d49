import styled from 'styled-components/native';

export const SettingsItemContainer = styled.View`
  background-color: #fff;
`;

export const SettingsTouchable = styled.TouchableOpacity`
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: 16px 0;
  background-color: #fff;
`;

export const SettingsTouchableText = styled.Text`
  font-family: 'MuktaVaaniLight';
  font-size: 16px;
`;

export const SelectedText = styled.Text`
  max-width: 140px;
  color: #333;
  font-family: 'MuktaVaaniSemiBold';
  font-size: 16px;
  line-height: 24px;
`;

export const SettingsItemChildrenContainer = styled.View`
  margin-bottom: 16px;
`;
