import styled from 'styled-components/native';
import { DefaultTheme } from 'styled-components';
export const CustomTabBar = styled.View`
  height: 56px;
  background-color: #F0F0F0;
  width: 100%;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: 0 16px;
`;

export const NavText = styled.Text<{ isFocused: boolean }>`
  font-family: MuktaVaaniSemiBold;
  font-size: 12px;
  color: ${({ isFocused, theme }: { isFocused: boolean, theme: DefaultTheme }) => isFocused ? theme.brand.green[500] : '#A8A8A8'};
`;

export const CustomTabBarItem = styled.TouchableOpacity`
  width: 40px;
  height: 40px;
  background-color: ${({ theme }: { theme: DefaultTheme }) => theme.brand.green[500]};
  border-radius: 50px;
  justify-content: center;
  align-items: center;
`;

export const TabBarItemContainer = styled.TouchableOpacity`
  justify-content: center;
  align-items: center;
`;

export const AddButtonContainer = styled.View`
  position: absolute;
  top: -40px;
  align-self: center;
  justify-content: center;
  align-items: center;
  gap: 4px;
`;


