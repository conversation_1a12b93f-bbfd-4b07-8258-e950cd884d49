import { ActivityIndicator, FlatList, LayoutChangeEvent, TouchableOpacity, View, GestureResponderEvent } from 'react-native';
import {
  TemplateModalContainer,
  TemplateModalHeader,
  TemplateModalTitle,
  HeaderText,
  TemplateItemContainer,
  TemplateItemName,
  TemplateItemDescription,
  TemplatesContainer,
  CategoryItem,
  CategoryItemText,
  TemplateImage,
  TemplatePreviewContainer,
  TemplatePreviewHeader,
  TemplatePreviewTitle,
  TemplatePreviewDescription,
  TemplateItemsList,
  TemplateItemRow,
  TemplateItemCheckbox,
  TemplatePreviewItemName,
  TemplateItemQuantity,
} from './styles';
import Modal from 'react-native-modal';
import { useState, useEffect, use, useCallback, useMemo } from 'react';
import { getTemplate } from '@/methods/trips';  
import { SubtitleText, TitleSection } from '../common/Text';
import { Text } from 'react-native';
import { Check } from 'lucide-react-native';

interface TemplateItem {
  id: string;
  name: string;
  quantity?: number;
  isChecked?: boolean;
}

interface Template {
  id: string;
  name: string;
  description: string;
  category: string;
  image: string;
  items: TemplateItem[];
}

interface TemplateListModalProps {
  isVisible: boolean;
  onClose: () => void;
  onSelect: (template: Template) => void;
  setShowEmptyState: (show: boolean) => void;
}

type ViewMode = 'list' | 'preview';

export default function TemplateListModal({ isVisible, onClose, onSelect, setShowEmptyState }: TemplateListModalProps) {
  const { data: templates, isLoading } = getTemplate();
  const [viewMode, setViewMode] = useState<ViewMode>('list');
  const [selectedTemplate, setSelectedTemplate] = useState<Template | null>(null);
  const [width, setWidth] = useState(0);
  const [selectedCategory, setSelectedCategory] = useState<any>({ id: 'all', name: 'All', category: 'All' });
  const [checkedItems, setCheckedItems] = useState<string[]>([]);
  const [lastToggledItem, setLastToggledItem] = useState<string | null>(null);

  const handleLayout = (event: LayoutChangeEvent) => {
    const { width } = event.nativeEvent.layout;
    setWidth(width);
  };

  const getFilteredTemplates = () => {
    if (selectedCategory && selectedCategory.category !== 'All') {
      return templates?.packingListTemplates?.filter((item: Template) => item.category === selectedCategory.category);
    }
    return templates?.packingListTemplates;
  };

  const handleSelectTemplate = (template: Template) => {
    setSelectedTemplate(template);
    setCheckedItems(template.items.map(item => item.name));
    setViewMode('preview');
    setShowEmptyState(false);
  };

  const handleUseTemplate = useCallback(() => {
    if (selectedTemplate) {

      const newSelectedTemplate = { ...selectedTemplate };

      const selectedItems = newSelectedTemplate.items.filter(item => checkedItems.includes(item.name));

      newSelectedTemplate.items = selectedItems;
      console.log(newSelectedTemplate,'22', checkedItems);
      onSelect(newSelectedTemplate);
      onClose();
    }
  }, [selectedTemplate, checkedItems, onSelect, onClose]);

  const handleBack = () => {
    if (viewMode === 'preview') {
      setViewMode('list');
      setSelectedTemplate(null);
    } else {
      onClose();
    }
  };

  const toggleItemCheck = useCallback((itemId: string) => {
    let newCheckedItems = [...checkedItems];
    if(newCheckedItems.includes(itemId)) {
      newCheckedItems.splice(newCheckedItems.indexOf(itemId), 1);
    } else {
      newCheckedItems = [...newCheckedItems, itemId];
    }
    console.log(newCheckedItems,'22', checkedItems);
    setCheckedItems([...newCheckedItems]);
    setLastToggledItem(itemId);
  }, [checkedItems]);

  const toggleAllItems = useCallback(() => {
    if (!selectedTemplate?.items) return;
    
    setLastToggledItem(null);
    setCheckedItems(prev => {
      // If all items are checked, uncheck all
      const allItemsChecked = selectedTemplate.items.every(item => prev.includes(item.id));
      if (allItemsChecked) {
        return [];
      }
      // Otherwise, check all items
      return selectedTemplate.items.map(item => item.id);
    });
  }, [selectedTemplate?.items]);

  const getCheckedCount = useCallback(() => {
    return checkedItems.length;
  }, [checkedItems]);

  const getTotalItems = useCallback(() => {
    return selectedTemplate?.items?.length || 0;
  }, [selectedTemplate?.items]);

  const isAllChecked = useCallback(() => {
    if (!selectedTemplate?.items?.length) return false;
    return selectedTemplate.items.every(item => checkedItems.includes(item.id));
  }, [selectedTemplate?.items, checkedItems]);

  const isPartiallyChecked = useCallback(() => {
    if (!selectedTemplate?.items?.length) return false;
    const checkedCount = getCheckedCount();
    return checkedCount > 0 && checkedCount < getTotalItems();
  }, [selectedTemplate?.items, getCheckedCount, getTotalItems]);

  const handleRowPress = (itemId: string) => {
    toggleItemCheck(itemId);
  };

  const renderHeader = () => (
    <TemplateModalHeader>
      <TouchableOpacity onPress={handleBack}>
        <HeaderText>{viewMode === 'preview' ? 'Back' : 'Close'}</HeaderText>
      </TouchableOpacity>

      {viewMode === 'preview' && (
        <TouchableOpacity onPress={handleUseTemplate}>
          <HeaderText>Use Template</HeaderText>
        </TouchableOpacity>
      )}
      {viewMode === 'list' && <View style={{ width: 50 }} />}
    </TemplateModalHeader>
  );

  const renderContent = () => {
    if (viewMode === 'preview' && selectedTemplate) {
      return (
        <TemplatePreviewContainer>
          <TemplatePreviewHeader>
            <TemplatePreviewTitle>{selectedTemplate.name}</TemplatePreviewTitle>
            <TemplatePreviewDescription>{selectedTemplate.description}</TemplatePreviewDescription>
          </TemplatePreviewHeader>
          
          <TemplateItemsList>
            {selectedTemplate.items?.map((item) => {
              console.log(item,'22', checkedItems);
              const isChecked = checkedItems.includes(item.name);
              const isLastToggled = lastToggledItem === item.name;
              
              return (
                <TemplateItemRow 
                  key={item.id}
                  onPress={() => handleRowPress(item.name)}
                  activeOpacity={0.7}
                >
                  <TemplateItemCheckbox 
                    isChecked={isChecked}
                    onPress={(e: GestureResponderEvent) => {
                      e.stopPropagation();
                      toggleItemCheck(item.name);
                    }}
                  >
                    {isChecked && (
                      <Check 
                        size={16} 
                        color="#FFFFFF" 
                        strokeWidth={3}
                        style={{
                          transform: [{ scale: isLastToggled ? 1.1 : 1 }],
                        }}
                      />
                    )}
                  </TemplateItemCheckbox>
                  <TemplatePreviewItemName 
                    style={{
                      color: isChecked ? '#666666' : '#333333',
                    }}
                  >
                    {item.name}
                  </TemplatePreviewItemName>
         
                </TemplateItemRow>
              );
            })}
          </TemplateItemsList>
        </TemplatePreviewContainer>
      );
    }

    return (
      <>
        <View style={{ marginTop: 20 }}>
          <TitleSection string={'Packing List Templates'} />
          <SubtitleText string={'Explore these curated packing lists crafted by our top Muses — from weekend getaways to exotic adventures, these Myuse-approved templates make packing a breeze. Copy or customize one to start building your perfect suitcase!'} />
        </View>

        <TemplatesContainer onLayout={handleLayout} style={{ marginTop: 20 }}>
          {isLoading ? (
            <ActivityIndicator size="large" />
          ) : (
            <FlatList
              keyExtractor={(item) => item.id}
              numColumns={3}
              columnWrapperStyle={{justifyContent: 'space-between'}}
              contentContainerStyle={{width: width, paddingBottom: 200, gap: 20, justifyContent: 'space-between' }}
              data={getFilteredTemplates()}
              renderItem={({ item }) => (
                <TouchableOpacity onPress={() => handleSelectTemplate(item)}>
                  <TemplateItemContainer width={width / 3}>
                    <TemplateImage source={{ uri: item.image }} />
                    <TemplateItemName>{item.name}</TemplateItemName>
                  </TemplateItemContainer>
                </TouchableOpacity>
              )}
            />
          )}
        </TemplatesContainer>
      </>
    );
  };

  return (
    <Modal
      style={{ justifyContent: 'flex-end', margin: 0 }}
      isVisible={isVisible}
      onBackButtonPress={handleBack}
      onBackdropPress={handleBack}
    >
      <TemplateModalContainer>
        {renderHeader()}
        {renderContent()}
      </TemplateModalContainer>
    </Modal>
  );
} 