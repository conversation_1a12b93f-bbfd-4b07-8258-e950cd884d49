import { getUserProfile } from '@/methods/users';
import { Fragment, useEffect, useState } from 'react';
import { SettingsItem, SettingsItemProps } from '../SettingsItem';
import { Divider, SettingsGroupContainer } from './styles';

type SettingsGroupProps = {
  buttons?: SettingsItemProps[];
};

export const SettingsGroup = ({ buttons }: SettingsGroupProps) => {
  const [userPreferences, setUserPreferences] = useState(null);
  const { data: userProfile } = getUserProfile();

  useEffect(() => {
    userProfile?.data?.preferences &&
      setUserPreferences(userProfile.data.preferences);
  }, [userProfile]);

  return (
    <SettingsGroupContainer>
      {buttons?.map((button, index) => {
        const itemKey = button.itemKey || '';
        const selectedOption = button.options?.find(
          (option) => option.value === userPreferences?.[itemKey],
        ) || button.default;

        return (
          <Fragment key={index}>
            <SettingsItem {...button} selected={selectedOption} />
            {index !== buttons.length - 1 && <Divider />}
          </Fragment>
        );
      })}
    </SettingsGroupContainer>
  );
};
