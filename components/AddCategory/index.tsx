import { ActivityIndicator, FlatList, KeyboardAvoidingView, LayoutChangeEvent, TouchableOpacity, View } from 'react-native';
import {
  AddClothsModalContainer,
  AddClothsModalHeader,
  AddClothsModalTitle,
  HeaderText,
  AddClothsModalInput,
  ClothItemContainer,
  ClothItemName,
  ClothesContainer,
  AddClothItemContainer,
  CheckBox,
  CategoryItem,
  CategoryItemText,
  HeaderTitle
} from './styles';
import Modal from 'react-native-modal';
import { XIcon, PlusIcon, CheckIcon } from 'lucide-react-native';
import Input from '../common/Input';
import { useState, useEffect } from 'react';
import { CLOTHES, CLOTHES_CATEGORIES } from '@/constants/Clothes';
import Button from '../common/Button';

interface AddClothsCategoryModalProps {
  isVisible: boolean;
  onClose: () => void;
  onAdd: (item: any) => void;
  item: any;
  packingList: any[];
  setIsTemplateListModalVisible: (visible: boolean) => void;
}

export default function AddCategoryModal({ isVisible, onClose, onAdd, item, packingList = [], setIsTemplateListModalVisible }: AddClothsCategoryModalProps) {

  const [categoryName, setCategoryName] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [clothes, setClothes] = useState<any[]>([]);
  const [width, setWidth] = useState(0);
  const [selectedClothes, setSelectedClothes] = useState<any[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<any>({ id: 'all', name: 'All', category: 'All' });

  const handleLayout = (event: LayoutChangeEvent) => {
    const { width } = event.nativeEvent.layout;
    setWidth(width);
  };

  return <Modal
    key={item?.id}
    style={{ justifyContent: 'flex-end', margin: 0 }}
    isVisible={isVisible} onBackButtonPress={onClose} onBackdropPress={onClose}>
      <KeyboardAvoidingView behavior="padding" style={{ flex: 1, justifyContent: 'flex-end' }}>
        <AddClothsModalContainer>
          <View style={{ marginBottom: 10,  alignItems: 'center', justifyContent: 'space-between' }}>
      
      </View>
      <AddClothsModalHeader>
        <TouchableOpacity style={{ flex: 1 }} onPress={onClose}>
          <HeaderText>Close</HeaderText>
        </TouchableOpacity>
        <View style={{ flex: 2, alignItems: 'center' }}>
          <HeaderTitle>Add New Category</HeaderTitle>
        </View>
        <View style={{ flex: 1, alignItems: 'flex-end' }}>
          {/* <TouchableOpacity onPress={() => {
            onAdd(categoryName);
            setCategoryName('');
            onClose();
          }}>
            <HeaderText>Add</HeaderText>
          </TouchableOpacity> */}
        </View>
      </AddClothsModalHeader>
      <View style={{ marginTop: 20 }}>
        <AddClothsModalInput placeholder="Category Name"
          value={categoryName}
          onChangeText={setCategoryName}
        />
      </View>
      <ClothesContainer
        onLayout={handleLayout}
        style={{ marginTop: 20 }}>
          <Button title="Add Category" onPress={() => {
            onAdd(categoryName);
            setCategoryName('');
            onClose();
          }} />
          <View style={{  alignItems: 'center',flex: 1, justifyContent: 'center' }}>
            <HeaderText>Or</HeaderText>
          </View>
          <Button title="Add Packing Template" onPress={() => {
            onClose();
            setTimeout(() => {
              setIsTemplateListModalVisible(true);
            }, 1000);

          }} />

      </ClothesContainer>
    </AddClothsModalContainer>
    </KeyboardAvoidingView>
  </Modal>;
}