import { SettingsGroup } from '@/components/SettingsGroup';
import { SettingsSection } from '@/components/SettingsSection';
import HeaderPage from '@/components/common/HeaderPage';
import {
  BOTTOMS,
  FOOTWEAR,
  GENERAL_APPAREL,
  SIZING__SETTING,
} from '@/constants/settings';
import { View } from 'react-native';
import { getSizingCharts } from '@/methods/preferences';
import { useMemo } from 'react';
import { getUserProfile } from '@/methods/users';
import { SettingsItemProps } from '@/components/SettingsItem';

export default function SizingPreferences() {
  // Get user profile to determine gender
  const { data: userProfile } = getUserProfile();

  // Determine user gender (convert to API format)
  const userGender = useMemo(() => {
    const profileGender = userProfile?.data?.profile?.gender;
    if (profileGender === 'Male') return 'men';
    if (profileGender === 'Female') return 'women';
    return 'men'; // Default fallback
  }, [userProfile]);

  // Fetch sizing charts based on user gender
  const { data: sizingChartsData } = getSizingCharts(userGender);

  // Extract US sizing data and create dynamic options
  const dynamicSizingOptions = useMemo(() => {
    if (!sizingChartsData?.data?.sizingCharts) {
      return {
        generalApparel: GENERAL_APPAREL,
        bottoms: BOTTOMS,
        footwear: FOOTWEAR
      };
    }

    const charts = sizingChartsData.data.sizingCharts;

    // Helper function to extract US sizes from a chart
    const extractUSSizes = (chart: any) => {
      const usRegion = chart.regions?.find((region: any) => region.name === 'US');
      if (!usRegion?.sizes) return [];

      return usRegion.sizes.map((size: any, index: number) => ({
        label: typeof size === 'string' ? size : size.label || size.name || `Size ${index + 1}`,
        value: typeof size === 'string' ? size.toLowerCase() : size.value || size.label?.toLowerCase() || `size_${index + 1}`
      }));
    };

    // Find the appropriate chart based on user gender
    const topsChart = charts.find((c: any) =>
      c.type === (userGender === 'men' ? 'mensTops' : 'womensTops')
    );
    const trousersChart = charts.find((c: any) =>
      c.type === (userGender === 'men' ? 'mensTrousers' : 'womensTrousers')
    );
    const jeansChart = charts.find((c: any) =>
      c.type === (userGender === 'men' ? 'mensJeans' : 'womensJeans')
    );
    const outerwearChart = charts.find((c: any) =>
      c.type === (userGender === 'men' ? 'mensOuterwear' : 'womensDresses')
    );
    const shoesChart = charts.find((c: any) =>
      c.type === (userGender === 'men' ? 'mensShoes' : 'womensFootwear')
    );

    // Create dynamic general apparel options (map to original subcategories)
    const dynamicGeneralApparel: SettingsItemProps[] = [];

    if (topsChart) {
      const topsSizes = extractUSSizes(topsChart);
      if (topsSizes.length > 0) {
        // Map tops sizes to T-Shirt, Shirt, Sweater
        dynamicGeneralApparel.push(
          {
            title: 'T-Shirt',
            itemKey: 'tshirtSize',
            options: topsSizes
          },
          {
            title: 'Shirt',
            itemKey: 'shirtSize',
            options: topsSizes
          },
          {
            title: 'Sweater',
            itemKey: 'sweaterSize',
            options: topsSizes
          }
        );
      }
    }

    if (outerwearChart) {
      const outerwearSizes = extractUSSizes(outerwearChart);
      if (outerwearSizes.length > 0) {
        dynamicGeneralApparel.push({
          title: 'Jacket/Coat',
          itemKey: 'jacketCoat',
          options: outerwearSizes
        });
      }
    }

    // Create dynamic bottoms options (map to original subcategories)
    const dynamicBottoms: SettingsItemProps[] = [];

    if (trousersChart) {
      const trousersSizes = extractUSSizes(trousersChart);
      if (trousersSizes.length > 0) {
        // Map trousers sizes to Waist, Hip, Pants
        dynamicBottoms.push(
          {
            title: 'Waist',
            itemKey: 'waist',
            options: trousersSizes
          },
          {
            title: 'Hip',
            itemKey: 'hip',
            options: trousersSizes
          },
          {
            title: 'Pants',
            itemKey: 'pants',
            options: trousersSizes
          }
        );
      }
    }

    if (jeansChart) {
      const jeansSizes = extractUSSizes(jeansChart);
      if (jeansSizes.length > 0) {
        dynamicBottoms.push({
          title: 'Shorts',
          itemKey: 'shorts',
          options: jeansSizes
        });
      }
    }

    // Create dynamic footwear options (map to original subcategories)
    const dynamicFootwear: SettingsItemProps[] = [];

    if (shoesChart) {
      const shoesSizes = extractUSSizes(shoesChart);
      if (shoesSizes.length > 0) {
        dynamicFootwear.push(
          {
            title: 'Shoe',
            itemKey: 'shoe',
            options: shoesSizes
          },
          {
            title: 'Boots',
            itemKey: 'boots',
            options: shoesSizes
          }
        );
      }
    }

    return {
      generalApparel: dynamicGeneralApparel.length > 0 ? dynamicGeneralApparel : GENERAL_APPAREL,
      bottoms: dynamicBottoms.length > 0 ? dynamicBottoms : BOTTOMS,
      footwear: dynamicFootwear.length > 0 ? dynamicFootwear : FOOTWEAR
    };
  }, [sizingChartsData, userGender]);

  return (
    <>
      <HeaderPage title="Sizing Preferences" />
      <View style={{ gap: 24, marginTop: 24 }}>
        <SettingsSection title="Setting">
          <SettingsGroup buttons={SIZING__SETTING} />
        </SettingsSection>
        <SettingsSection title="General Apparel">
          <SettingsGroup buttons={dynamicSizingOptions.generalApparel} />
        </SettingsSection>
        <SettingsSection title="Bottoms">
          <SettingsGroup buttons={dynamicSizingOptions.bottoms} />
        </SettingsSection>
        <SettingsSection title="Footwear">
          <SettingsGroup buttons={dynamicSizingOptions.footwear} />
        </SettingsSection>
      </View>
    </>
  );
}
