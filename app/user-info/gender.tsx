import Button from '@/components/common/Button';
import HeaderPage from '@/components/common/HeaderPage';
import SectionTitle from '@/components/common/SectionTitle';
import Text from '@/components/common/Text';
import { useRouter } from 'expo-router';
import { useEffect, useState } from 'react';
import { Pressable, View } from 'react-native';

import { updateUserProfile, getUserProfile } from '@/methods/users';

import GenderSelectionItem from '@/components/common/GenderSelectionItem';

const GENDER_OPTIONS = [
  {
    title: 'Female',
  },
  {
    title: 'Male',
  },
  {
    title: 'Non-binary',
  },
  {
    title: 'Prefer not to say',
  },
];

export default function Gender() {

  const { mutate: updateProfile, isPending, data } = updateUserProfile();
  const { data: userProfile, isLoading } = getUserProfile();

  const router = useRouter();
  const [selectedGender, setSelectedGender] = useState<string | null>();

  useEffect(() => {
    if (userProfile?.data?.profile?.gender) {
      console.log('22');
      setSelectedGender(userProfile.data.profile.gender);
    }
  }, [isLoading]);


  useEffect(() => {
    if (data?.success) {
      router.push('/user-info/birthday');
    }
  }, [isPending]);

  const handleNext = () => {
    //save gender to server 
    updateProfile({ gender: selectedGender });
  };

  const handleSkip = () => {
    // updateProfile({ gender: null });
    router.push('/user-info/birthday');
  }

  return (
    <View style={{ height: '100%', justifyContent: 'space-between' }}>
      <View >
        <View>
          <HeaderPage
            disableBack={true}
            rightComponent={<Pressable onPress={handleSkip}><Text string="Skip" /></Pressable>}
          />
          <View style={{ marginTop: 32 }}>
            <SectionTitle title="Gender" />
          </View>
          <View style={{ marginTop: 16, gap: 16 }}>
            <Text string="Tell us about yourself so we can personalise your experience. We’re excited to get to know you!" />
          </View>

          <View style={{
            marginTop: 32, gap: 16, flexDirection: 'row', flexWrap: 'wrap', justifyContent: 'space-between',
          }}>
            {GENDER_OPTIONS.map((item) => (
              <GenderSelectionItem
                isSelected={selectedGender === item.title}
                key={item.title}
                title={item.title}
                onPress={() => setSelectedGender(item.title)}
              />
            ))}
          </View>
        </View>
      </View>
      <View style={{ marginTop: 32, gap: 16 }}>
        <Button
          title="Next"
          isLoading={isPending}
          isDisabled={!selectedGender}
          onPress={handleNext}
        />
      </View>
    </View>
  );
}
