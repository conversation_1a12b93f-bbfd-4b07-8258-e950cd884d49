import CalendarList from '@/components/CalendarList';
import CalendarSelection from '@/components/CalendarSelection';
import HeaderDashboard from '@/components/common/HeaderDashboard';
import TodayComponent from '@/components/common/TodayComponent';
import UpcomingTrips from '@/components/UpcomingTrips';
import { getUserProfile } from '@/methods/users';
import { ScrollView, View } from 'react-native';

export default function HomeScreen() {
  const { data: userProfile } = getUserProfile();

  const profile = userProfile?.data.profile;
  const firstName = profile?.name ? profile.name.split(' ')[0] : '';
  const displayName = firstName || profile?.userName || profile?.email;

  return (
    <View>
      <HeaderDashboard title={`Welcome, ${displayName}!`} />
      <ScrollView
        showsHorizontalScrollIndicator={false}
        showsVerticalScrollIndicator={false}
        bounces={false}
        contentContainerStyle={{ paddingBottom: 220 }}
      >
        {/* <TagLocation /> */}
        <TodayComponent />
        <CalendarSelection />
        <CalendarList />
        <UpcomingTrips />
      </ScrollView>
    </View>
  );
}
