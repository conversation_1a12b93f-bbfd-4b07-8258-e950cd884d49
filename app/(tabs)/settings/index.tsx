import { SettingsGroup } from '@/components/SettingsGroup';
import { SettingsSection } from '@/components/SettingsSection';
import Button from '@/components/common/Button';
import HeaderPage from '@/components/common/HeaderPage';
import { useSession } from '@/config/ctx';
import {
  ACCOUNT_SETTINGS,
  LEGAL_POLICIES,
  PREFERENCES,
} from '@/constants/settings';
import { useProfile } from '@/context/ProfileContext';
import Meteor from '@meteorrn/core';
import { router } from 'expo-router';
import { View } from 'react-native';

export default function Settings() {
  const { signOut } = useSession();
  const { onDeletePress } = useProfile();

  return (
    <>
      <HeaderPage
        title="Settings"
        backCallback={() => router.push('/(tabs)/profile')}
      />
      <View style={{ gap: 24, marginTop: 24, paddingBottom: 20 }}>
        <SettingsSection title="Account and Settings">
          <SettingsGroup buttons={ACCOUNT_SETTINGS} />
        </SettingsSection>
        <SettingsSection title="Preferences">
          <SettingsGroup buttons={PREFERENCES} />
        </SettingsSection>
        <SettingsSection title="Legal Policies">
          <SettingsGroup buttons={LEGAL_POLICIES} />
        </SettingsSection>
        <Button
          title="Logout"
          buttonColor="red"
          onPress={() => {
            Meteor.logout(() => {
              router.replace('/onboarding');
              signOut();
            });
          }}
        />
        <Button
          title="Delete Account"
          buttonColor="red"
          isTransparent
          onPress={onDeletePress}
        />
      </View>
    </>
  );
}
