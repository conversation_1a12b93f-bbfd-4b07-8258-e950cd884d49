import { Accordion } from '@/components/Accordion';
import { Tags } from '@/components/Tags';
import HeaderPage from '@/components/common/HeaderPage';
import SectionTitle from '@/components/common/SectionTitle';
import Text from '@/components/common/Text';
import { STYLE_HARDPASSES } from '@/constants/style-preferences';
import {
  getStylePreferences,
  updateStyleHardPasses,
} from '@/methods/preferences';
import { useQueryClient } from '@tanstack/react-query';
import { useCallback } from 'react';
import { View } from 'react-native';

export default function StyleHardPasses() {
  const queryClient = useQueryClient();
  const { data } = getStylePreferences();
  const { mutate: updateHardPasses } = updateStyleHardPasses();

  const onToggleTag = useCallback(
    (selectedTags: string[]) => {
      updateHardPasses(
        { hardPasses: selectedTags },
        {
          onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['stylePreferences'] });
          },
        },
      );
    },
    [queryClient],
  );

  return (
    <>
      <HeaderPage noLogo />
      <View style={{ marginTop: 16 }}>
        <SectionTitle title="Style Hardpasses" />
      </View>
      <View style={{ gap: 24, marginTop: 24 }}>
        <Text string="Tell us your style hardpasses – the trends, fits, or colors you absolutely avoid! Whether it’s cropped tops, skinny jeans, loud prints, or certain fabrics, let us know what doesn’t work for you so we can tailor recommendations to your preferences." />
        {STYLE_HARDPASSES.map((item) => {
          return (
            <Accordion title={item.title} key={item.id}>
              <Tags
                initialTags={item.keywords.map((tag) => ({
                  text: tag,
                  selected:
                    data?.data?.stylePreferences?.hardPasses?.includes(tag) ||
                    false,
                }))}
                onToggleTag={onToggleTag}
              />
            </Accordion>
          );
        })}
      </View>
    </>
  );
}
