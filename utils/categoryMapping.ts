/**
 * Utility functions for mapping category IDs to category names
 * Used by both Closet and PackingList components for consistent category filtering
 */

// Create a mapping from backend category IDs to category names
export const createCategoryIdToNameMap = (categories: any[]): Map<string, string> => {
  const idToNameMap = new Map<string, string>();
  
  if (!categories || !Array.isArray(categories)) {
    return idToNameMap;
  }
  
  categories.forEach((category: any) => {
    // Handle new schema with mainCategory
    if (category._id && category.mainCategory) {
      idToNameMap.set(category._id.toLowerCase(), category.mainCategory.toLowerCase());
    }
    // Handle old schema with name (fallback)
    else if (category._id && category.name) {
      idToNameMap.set(category._id.toLowerCase(), category.name.toLowerCase());
    }
    
    // Also map subcategories if they exist
    if (category.subCategories && Array.isArray(category.subCategories)) {
      category.subCategories.forEach((subCategory: any) => {
        if (subCategory._id && subCategory.name) {
          // Map subcategory to main category
          const mainCategoryName = category.mainCategory || category.name;
          if (mainCategoryName) {
            idToNameMap.set(subCategory._id.toLowerCase(), mainCategoryName.toLowerCase());
          }
        }
      });
    }
  });
  
  return idToNameMap;
};

// Extract category name from an item using multiple fallback strategies
export const getItemCategoryName = (item: any, categoryIdToNameMap?: Map<string, string>): string => {
  // Try different category fields in order of preference
  
  // 1. Try category.mainCategory (new schema)
  if (item?.category?.mainCategory) {
    return item.category.mainCategory.toLowerCase().trim();
  }
  
  // 2. Try category.name (old schema)
  if (item?.category?.name) {
    return item.category.name.toLowerCase().trim();
  }
  
  // 3. Try category as string
  if (typeof item?.category === 'string') {
    return item.category.toLowerCase().trim();
  }
  
  // 4. Try categoryName field
  if (item?.categoryName) {
    return item.categoryName.toLowerCase().trim();
  }
  
  // 5. Handle itemCategoryId
  if (item?.itemCategoryId) {
    const itemCategoryId = item.itemCategoryId.toLowerCase().trim();
    
    // First, try to map the ID to a category name using backend data
    if (categoryIdToNameMap && categoryIdToNameMap.has(itemCategoryId)) {
      return categoryIdToNameMap.get(itemCategoryId)!;
    }
    
    // Handle basic- prefixed categories (general categories)
    if (item.itemCategoryId.startsWith('basic-')) {
      return item.itemCategoryId.replace('basic-', '').toLowerCase().trim();
    }
    
    // Handle standard- prefixed categories
    if (item.itemCategoryId.startsWith('standard-')) {
      return item.itemCategoryId.replace('standard-', '').toLowerCase().trim();
    }
    
    // If it's already a category name (like "dresses", "shoes"), use it directly
    return item.itemCategoryId.toLowerCase().trim();
  }
  
  return '';
};

// Extract unique categories from a list of items
export const getUsedCategories = (items: any[], categoryIdToNameMap?: Map<string, string>): Set<string> => {
  const usedCategories = new Set<string>();

  if (!items || !Array.isArray(items)) {
    return usedCategories;
  }

  items.forEach((item) => {
    const categoryName = getItemCategoryName(item, categoryIdToNameMap);
    if (categoryName) {
      usedCategories.add(categoryName);
    }
  });

  return usedCategories;
};

// Filter items by category
export const filterItemsByCategory = (
  items: any[], 
  selectedCategoryName: string, 
  categoryIdToNameMap?: Map<string, string>
): any[] => {
  if (!items || !Array.isArray(items)) {
    return [];
  }

  // If "All" category is selected, return all items
  if (selectedCategoryName.toLowerCase() === 'all') {
    return items;
  }

  return items.filter((item) => {
    const itemCategoryName = getItemCategoryName(item, categoryIdToNameMap);
    if (!itemCategoryName) {
      return false;
    }

    // Exact match (case-insensitive)
    return itemCategoryName === selectedCategoryName.toLowerCase().trim();
  });
};

// Filter display categories to show only those with items
export const filterCategoriesWithItems = (
  displayCategories: any[],
  items: any[],
  categoryIdToNameMap?: Map<string, string>
): any[] => {
  if (!displayCategories || !items) {
    return [{ _id: 'all', name: 'All', category: 'All' }];
  }

  // Get categories that have items
  const usedCategories = getUsedCategories(items, categoryIdToNameMap);

  // Filter displayCategories to show only those that have items
  const filteredCategories = displayCategories.filter((category: any) => {
    // Always include "All" category
    if (category.name === 'All') {
      return true;
    }
    
    // Check if this category has items
    const categoryNameLower = category.name.toLowerCase().trim();
    return usedCategories.has(categoryNameLower);
  });

  // Sort categories alphabetically (keeping "All" first)
  const sortedCategories = filteredCategories.sort((a: any, b: any) => {
    // Keep "All" category first
    if (a.name === 'All') return -1;
    if (b.name === 'All') return 1;
    
    // Sort others alphabetically
    return a.name.localeCompare(b.name);
  });

  return sortedCategories;
};
