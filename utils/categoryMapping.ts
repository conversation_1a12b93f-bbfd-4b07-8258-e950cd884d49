export const createCategoryIdToNameMap = (categories: any[]): Map<string, string> => {
  const idToNameMap = new Map<string, string>();

  if (!categories || !Array.isArray(categories)) {
    return idToNameMap;
  }

  categories.forEach((category: any) => {
    if (category._id && category.mainCategory) {
      idToNameMap.set(category._id.toLowerCase(), category.mainCategory.toLowerCase());
    }
    else if (category._id && category.name) {
      idToNameMap.set(category._id.toLowerCase(), category.name.toLowerCase());
    }

    if (category.subCategories && Array.isArray(category.subCategories)) {
      category.subCategories.forEach((subCategory: any) => {
        if (subCategory._id && subCategory.name) {
          const mainCategoryName = category.mainCategory || category.name;
          if (mainCategoryName) {
            idToNameMap.set(subCategory._id.toLowerCase(), mainCategoryName.toLowerCase());
          }
        }
      });
    }
  });

  return idToNameMap;
};

export const getItemCategoryName = (item: any, categoryIdToNameMap?: Map<string, string>): string => {
  if (item?.category?.mainCategory) {
    return item.category.mainCategory.toLowerCase().trim();
  }

  if (item?.category?.name) {
    return item.category.name.toLowerCase().trim();
  }

  if (typeof item?.category === 'string') {
    return item.category.toLowerCase().trim();
  }

  if (item?.categoryName) {
    return item.categoryName.toLowerCase().trim();
  }
  if (item?.itemCategoryId) {
    const itemCategoryId = item.itemCategoryId.toLowerCase().trim();

    if (categoryIdToNameMap && categoryIdToNameMap.has(itemCategoryId)) {
      return categoryIdToNameMap.get(itemCategoryId)!;
    }

    if (item.itemCategoryId.startsWith('basic-')) {
      return item.itemCategoryId.replace('basic-', '').toLowerCase().trim();
    }

    if (item.itemCategoryId.startsWith('standard-')) {
      return item.itemCategoryId.replace('standard-', '').toLowerCase().trim();
    }

    if (isLikelyCategoryName(item.itemCategoryId)) {
      return item.itemCategoryId.toLowerCase().trim();
    }

    return generateCategoryFromItem(item);
  }

  return '';
};
const isLikelyCategoryName = (str: string): boolean => {
  const knownCategories = [
    'tops', 'bottoms', 'shoes', 'dresses', 'accessories', 'bags', 'jewelry', 'jewellery',
    'outerwear', 'activewear', 'loungewear', 'swimwear', 'underwear', 'sleepwear',
    'tech', 'technology', 'electronics', 'toiletries', 'skincare', 'haircare'
  ];

  return knownCategories.includes(str.toLowerCase()) || str.length < 15;
};
const generateCategoryFromItem = (item: any): string => {
  const itemName = (item.name || '').toLowerCase();

  if (itemName.includes('phone') || itemName.includes('laptop') || itemName.includes('tablet') ||
      itemName.includes('computer') || itemName.includes('charger') || itemName.includes('camera') ||
      itemName.includes('headphone') || itemName.includes('speaker') || itemName.includes('watch')) {
    return 'tech';
  }

  if (itemName.includes('shirt') || itemName.includes('top') || itemName.includes('blouse') ||
      itemName.includes('sweater') || itemName.includes('hoodie') || itemName.includes('jacket')) {
    return 'tops';
  }

  if (itemName.includes('pants') || itemName.includes('jeans') || itemName.includes('shorts') ||
      itemName.includes('skirt') || itemName.includes('trouser')) {
    return 'bottoms';
  }

  if (itemName.includes('shoe') || itemName.includes('boot') || itemName.includes('sneaker') ||
      itemName.includes('sandal') || itemName.includes('heel')) {
    return 'shoes';
  }

  if (itemName.includes('dress')) {
    return 'dresses';
  }

  if (itemName.includes('bag') || itemName.includes('purse') || itemName.includes('backpack') ||
      itemName.includes('wallet') || itemName.includes('clutch')) {
    return 'bags';
  }

  if (itemName.includes('necklace') || itemName.includes('ring') || itemName.includes('bracelet') ||
      itemName.includes('earring') || itemName.includes('jewelry')) {
    return 'jewelry';
  }

  if (itemName.includes('shampoo') || itemName.includes('soap') || itemName.includes('cream') ||
      itemName.includes('lotion') || itemName.includes('perfume') || itemName.includes('cologne')) {
    return 'toiletries';
  }

  if (itemName.includes('test')) {
    return 'others';
  }

  return 'others';
};

export const getUsedCategories = (items: any[], categoryIdToNameMap?: Map<string, string>): Set<string> => {
  const usedCategories = new Set<string>();

  if (!items || !Array.isArray(items)) {
    return usedCategories;
  }

  items.forEach((item) => {
    const categoryName = getItemCategoryName(item, categoryIdToNameMap);
    if (categoryName) {
      usedCategories.add(categoryName);
    }
  });

  return usedCategories;
};

export const filterItemsByCategory = (
  items: any[],
  selectedCategoryName: string,
  categoryIdToNameMap?: Map<string, string>
): any[] => {
  if (!items || !Array.isArray(items)) {
    return [];
  }

  if (selectedCategoryName.toLowerCase() === 'all') {
    return items;
  }

  return items.filter((item) => {
    const itemCategoryName = getItemCategoryName(item, categoryIdToNameMap);
    if (!itemCategoryName) {
      return false;
    }

    return itemCategoryName === selectedCategoryName.toLowerCase().trim();
  });
};

export const filterCategoriesWithItems = (
  displayCategories: any[],
  items: any[],
  categoryIdToNameMap?: Map<string, string>
): any[] => {
  if (!displayCategories || !items) {
    return [{ _id: 'all', name: 'All', category: 'All' }];
  }

  const usedCategories = getUsedCategories(items, categoryIdToNameMap);

  const filteredCategories = displayCategories.filter((category: any) => {
    if (category.name === 'All') {
      return true;
    }

    const categoryNameLower = category.name.toLowerCase().trim();
    return usedCategories.has(categoryNameLower);
  });

  const sortedCategories = filteredCategories.sort((a: any, b: any) => {
    if (a.name === 'All') return -1;
    if (b.name === 'All') return 1;

    return a.name.localeCompare(b.name);
  });

  return sortedCategories;
};
